import json
import random
import string
import re
import uuid
import aiohttp
import asyncio
import aiofiles
import pyotp
import logging
import os
from bs4 import BeautifulSoup
from datetime import datetime
from pathlib import Path
from typing import Union, List, Dict, Set
from playwright.async_api import Page, BrowserContext
from utils.msmail import accounts, get_email_content
from utils.browser_fingerprint import BrowserFingerprint

class TargonRegisterTask:
    def __init__(
        self,
        account_file: Union[str, Path] = "data/targon_accounts.csv",
        error_file: Union[str, Path] = "data/targon_error_accounts.txt",
    ):
        self.logger = logging.getLogger(__name__)
        self.account_file = Path(account_file)
        self.error_file = Path(error_file)
        self.email_address = None
        self.manual_code = None  # 存储2FA手动码
        self.file_lock = asyncio.Lock()
        self.fingerprint_generator = BrowserFingerprint()

    async def get_registered_emails(self) -> Set[str]:
        """
        读取已经注册的账号，避免重复注册

        Returns:
            Set[str]: 已注册的邮箱集合
        """
        try:
            # 确保data目录存在
            os.makedirs(os.path.dirname(self.account_file), exist_ok=True)

            # 检查CSV文件是否存在
            if not self.account_file.exists():
                # 如果文件不存在，创建一个空文件
                async with aiofiles.open(self.account_file, "w", encoding="utf-8") as f:
                    await f.write("")
                return set()

            # 读取并解析CSV文件
            async with aiofiles.open(self.account_file, "r", encoding="utf-8") as f:
                content = await f.read()

            # 提取邮箱（CSV的第一列）
            emails = set()
            for line in content.strip().split("\n"):
                if line.strip():
                    email = line.split(",")[0]
                    emails.add(email.strip())

            self.logger.info(f"已从{self.account_file}读取{len(emails)}个已注册账号")
            return emails
        except Exception as e:
            self.logger.error(f"读取已注册账号时出错: {str(e)}")
            return set()

    async def get_error_emails(self) -> List[str]:
        """
        读取注册失败的错误账号，避免重复注册

        Returns:
            List[str]: 注册失败的邮箱列表
        """
        try:
            # 确保data目录存在
            os.makedirs(os.path.dirname(self.error_file), exist_ok=True)

            # 检查错误账号文件是否存在
            if not self.error_file.exists():
                # 如果文件不存在，创建一个空文件
                async with aiofiles.open(self.error_file, "w", encoding="utf-8") as f:
                    await f.write("")
                return []

            # 读取并解析文件
            async with aiofiles.open(self.error_file, "r", encoding="utf-8") as f:
                content = await f.read()

            emails = [
                line.strip() for line in content.strip().split("\n") if line.strip()
            ]

            self.logger.info(f"已从{self.error_file}读取{len(emails)}个注册失败的账号")
            return emails
        except Exception as e:
            self.logger.error(f"读取注册失败账号时出错: {str(e)}")
            return []

    async def save_error_account(self, email: str) -> None:
        """
        保存注册失败的账号到文件

        Args:
            email: 注册失败的邮箱
        """
        try:
            # 确保data目录存在
            os.makedirs(os.path.dirname(self.error_file), exist_ok=True)

            async with self.file_lock:
                async with aiofiles.open(self.error_file, "a", encoding="utf-8") as f:
                    await f.write(f"{email}\n")
            self.logger.info(f"成功保存注册失败账号 {email} 到 {self.error_file}")
        except Exception as e:
            self.logger.error(f"保存注册失败账号时出错: {str(e)}")
 

    def generate_password(self):
        """生成随机密码"""
        return "Dean0104@targon"
        length = 12
        chars = string.ascii_letters + string.digits + "!@#$%^&*"
        return ''.join(random.choice(chars) for _ in range(length))
    async def register_account_http(self, session: aiohttp.ClientSession, password: str):
        """注册Targon账户"""
        try:
            self.logger.info("开始注册账户...")

            register_data = {
                "0": {
                    "json": {
                        "email": self.email_address,
                        "password": password,
                        "turnstileToken": "0.sBkmqtFNZ9bndCpInlLMKv-gYBeOoLY6mP376iFur7x9ao9i1pmJORthtKXWMEk_tZWxwuga8M0m_e8WOVKfZNczI10sH91DstqrwPXQNoZ_aI824QmyZcSh21KqarimoW3ufLGuHCk71ebAj-EM_oYNByr6Z2zHs3L120zV3VgPljB4a310misuAktFXnclSVNv7cHHWBOuEKiI3uWrcWzJGxN2ej6p6R33oyAuCR1wAuOyJpIsF0A_Xw_mMakAK3mf7D0-6YMGl1QvKhmHYYmuz9Rl2Q9gS0kpn9n8XMNAum_ADRzzYemYKG-N6O8aFBYc8I7T7sfTOd1vKzravlP7mdAnOU6J_4bF1IhO1DI7NNfQJ5xUJYsZHgDAWraksViZdMfUUrQM73P7sVY9dwBnl1cDOp4uqYfPqD8M4MFjuX06Lg2YSK1CoZAWuFO4z-mlT70L644eHsMFhhvpEtvAoBxq3QKIgL9y6PjdvKt2amyloGncQPnZ5gOKC7uR660PjdC5EBoTPMUvKJB8sF8JbheNuRKJWolmthP_KnbHGmnSbbVe_1F1VAWBHFqrjxThSMjGfEcNinbAGaolEqAbzJYstUQFgjzERAHiDkQYHLR9GKI7E2V7mRJH-zZ8elpDOQ2Zq5IGh-uQDIWOOFAuek8PQN7yNh9rYP0x5azEdrAPiBv7QPu6cEXGCzVLI546_tb_8EaqiFrxceaEAN6vO_PtpIwuoDnpG305V9WpJ15FQtofe0Fshe1GSdzu0FHKwdaNW1gatncRoWNbXrUKLeVkV3rqGAYKss7buCfkQsECu2SJVoKrtjJDgfmG5EMD2Rhyry6KDlIZBfSHIDtUHIun-ySAQ2xqMhCOanJquc6pPkM0sTh1b2UtyS3_Kn-4y6luqqyCoza814HUdjxzFRnDH8Bnq6gP4sL2Kbs.yN5RtLNGBa5SdyJmnPZ80w.5e5cfb4ce32a5758d54019d1aa077ed055e69d3789805e684bec75d0d7cc7639"
                    }
                }
            }

            headers = self.headers.copy()
            headers.update({
                'Content-Type': 'application/json',
                'x-trpc-source': 'react'
            })

            async with session.post(
                "https://targon.com/api/trpc/account.createAccount?batch=1",
                json=register_data,
                headers=headers
            ) as response:
                if response.status == 200:
                    self.logger.info("账户注册成功")
                    return True
                else:
                    response_text = await response.text()
                    self.logger.error(f"注册失败: {response.status}, 响应内容: {response_text}")
                    return False

        except Exception as e:
            self.logger.error(f"注册异常: {e}")
            return False

    async def register_account(self, page: Page, password: str):
        """使用Playwright注册Targon账户"""
        try:
            self.logger.info("开始注册账户...")

            # 访问注册页面
            await page.goto("https://targon.com/sign-in?mode=signup", timeout=60000)
            await page.wait_for_load_state("networkidle")

            # 模拟人类行为
            #await self.fingerprint_generator.simulate_human_behavior(page)

            # 填写邮箱
            await page.fill('#email', self.email_address)
            self.logger.info(f"已填写邮箱: {self.email_address}")
            await page.wait_for_timeout(random.randint(500, 1000))

            # 点击继续按钮
            await page.click('button[type="submit"]')
            self.logger.info("已点击继续按钮")

            # 等待500ms
            await page.wait_for_timeout(500)

            # 填写密码
            await page.fill('#password', password)
            self.logger.info("已填写密码")
            await page.wait_for_timeout(random.randint(300, 800))

            # 填写确认密码
            await page.fill('#password2', password)
            self.logger.info("已填写确认密码")
            await page.wait_for_timeout(random.randint(300, 800))

            # 点击注册按钮
            await page.click('button[type="submit"]')
            self.logger.info("已点击注册按钮")

            # 等待注册完成
            await page.wait_for_timeout(3000)
            self.logger.info("账户注册成功")
            return True

        except Exception as e:
            self.logger.error(f"注册异常: {e}")
            return False

    async def get_activation_link(self, max_attempts=15, delay=3):
        """获取激活链接"""
        try:
            self.logger.info("等待激活邮件...")

            for attempt in range(max_attempts):
                self.logger.info(f"检查邮件 (第 {attempt + 1}/{max_attempts} 次)...")

                try:
                    email_content = await get_email_content(self.email_address)
                    if (
                        "<EMAIL>" in email_content and
                        "Targon" in email_content
                    ):
                        self.logger.info("找到Targon验证邮件!")

                        # 从HTML内容中提取激活链接
                        soup = BeautifulSoup(email_content, 'html.parser')
                        links = soup.find_all('a', href=True)

                        for link in links:
                            href = link['href']
                            if 'email-verification' in href and 'token=' in href:
                                self.logger.info("成功提取激活链接")
                                return href

                except Exception as e:
                    self.logger.warning(f"第{attempt+1}次获取邮件失败: {str(e)}")

                if attempt < max_attempts - 1:
                    self.logger.info(f"等待 {delay} 秒后重试...")
                    await asyncio.sleep(delay)

            self.logger.error("未能获取到激活链接")
            return None

        except Exception as e:
            self.logger.error(f"获取激活链接异常: {e}")
            return None

    async def activate_email(self, page: Page, activation_link: str):
        """使用Playwright激活邮箱"""
        try:
            self.logger.info("开始激活邮箱...")

            # 访问激活链接
            await page.goto(activation_link, timeout=60000)
            await page.wait_for_load_state("networkidle")

            # 等待页面加载完成
            await page.wait_for_timeout(2000)

            self.logger.info("邮箱激活成功")
            return True

        except Exception as e:
            self.logger.error(f"激活异常: {e}")
            return False

    async def setup_2fa(self, page: Page):
        """使用Playwright设置2FA"""
        try:
            self.logger.info("开始设置2FA...")

            # 访问2FA设置页面
            await page.goto("https://targon.com/two-factor-auth", timeout=60000)
            await page.wait_for_load_state("networkidle")

            # 等待页面加载完成
            await page.wait_for_timeout(2000)

            # 查找手动码元素并提取
            try:
                # 尝试多种可能的选择器来获取手动码
                manual_code_selectors = [
                    'code',
                    '[data-testid="manual-code"]',
                    '.manual-code',
                    'span:has-text("Manual")',
                    'div:has-text("Manual")'
                ]

                manual_code = None
                for selector in manual_code_selectors:
                    try:
                        element = await page.query_selector(selector)
                        if element:
                            text = await element.text_content()
                            if text and len(text.strip()) > 10:  # 手动码通常比较长
                                manual_code = text.strip()
                                break
                    except:
                        continue

                if not manual_code:
                    # 如果找不到手动码，尝试从页面内容中提取
                    page_content = await page.content()
                    # 使用正则表达式查找可能的手动码
                    import re
                    pattern = r'[A-Z2-7]{32}'  # Base32编码的手动码通常是32位
                    matches = re.findall(pattern, page_content)
                    if matches:
                        manual_code = matches[0]

                if manual_code:
                    self.manual_code = manual_code
                    self.logger.info(f"成功获取手动码: {manual_code[:8]}...")

                    # 生成TOTP验证码
                    totp = pyotp.TOTP(manual_code)
                    otp_code = totp.now()

                    # 查找并填写验证码输入框
                    otp_input_selectors = [
                        'input[type="text"]',
                        'input[placeholder*="code"]',
                        'input[name*="otp"]',
                        'input[id*="otp"]'
                    ]

                    for selector in otp_input_selectors:
                        try:
                            if await page.is_visible(selector):
                                await page.fill(selector, otp_code)
                                self.logger.info("已填写验证码")
                                break
                        except:
                            continue

                    # 查找并点击确认按钮
                    confirm_button_selectors = [
                        'button[type="submit"]',
                        'button:has-text("Enable")',
                        'button:has-text("Confirm")',
                        'button:has-text("Verify")'
                    ]

                    for selector in confirm_button_selectors:
                        try:
                            if await page.is_visible(selector):
                                await page.click(selector)
                                self.logger.info("已点击确认按钮")
                                break
                        except:
                            continue

                    await page.wait_for_timeout(2000)
                    self.logger.info("2FA设置成功")
                    return True
                else:
                    self.logger.error("未能获取到手动码")
                    return False

            except Exception as e:
                self.logger.error(f"设置2FA时出错: {e}")
                return False

        except Exception as e:
            self.logger.error(f"2FA设置异常: {e}")
            return False

    async def get_api_keys(self, page: Page):
        """使用Playwright获取API密钥"""
        try:
            self.logger.info("获取API密钥...")

            # 访问设置页面
            await page.goto("https://targon.com/settings", timeout=60000)
            await page.wait_for_load_state("networkidle")

            # 等待页面加载完成
            await page.wait_for_timeout(2000)

            # 尝试查找API密钥
            api_keys = []

            # 查找可能包含API密钥的元素
            api_key_selectors = [
                '[data-testid="api-key"]',
                '.api-key',
                'code',
                'input[readonly]',
                'span:has-text("tg-")',
                'div:has-text("tg-")'
            ]

            for selector in api_key_selectors:
                try:
                    elements = await page.query_selector_all(selector)
                    for element in elements:
                        text = await element.text_content()
                        if text and text.startswith('tg-'):
                            api_keys.append({'key': text.strip()})
                except:
                    continue

            if not api_keys:
                # 如果没有找到API密钥，尝试从页面内容中提取
                page_content = await page.content()
                import re
                # 查找以tg-开头的API密钥
                pattern = r'tg-[a-zA-Z0-9]+'
                matches = re.findall(pattern, page_content)
                for match in matches:
                    api_keys.append({'key': match})

            if api_keys:
                self.logger.info(f"获取到 {len(api_keys)} 个API密钥")
                return api_keys
            else:
                self.logger.warning("未找到API密钥")
                return []

        except Exception as e:
            self.logger.error(f"获取密钥异常: {e}")
            return []


    async def get_available_accounts(self) -> List[Dict]:
        """
        从msmail.py中获取可用的邮箱（排除已注册的和注册失败的）

        Returns:
            List[Dict]: 可用的账号列表
        """
        # 获取已注册的账号
        registered_emails = await self.get_registered_emails()
        # 获取注册失败的账号
        error_emails = await self.get_error_emails()

        # 过滤出未注册的账号且不在错误邮箱列表中的账号
        available_accounts = []
        for account in accounts:
            email = account.get("email")
            if email and email not in registered_emails and email not in error_emails:
                available_accounts.append(account)

        self.logger.info(f"找到{len(available_accounts)}个未注册的账号")
        return available_accounts

    async def save(self, email: str, password: str, api_keys: List[Dict]):
        """
        保存用户信息到文件，使用aiofiles和锁机制确保并行环境下的安全操作

        Args:
            email: 邮箱地址
            password: 密码
            api_keys: API密钥列表
        """
        try:
            # 确保data目录存在
            os.makedirs(os.path.dirname(self.account_file), exist_ok=True)

            # 提取第一个API密钥作为主要密钥
            main_api_key = ""
            if api_keys:
                main_api_key = api_keys[0].get('key', '')

            async with self.file_lock:  # 获取锁，确保同一时间只有一个任务能写文件
                async with aiofiles.open(
                    self.account_file, "a+", encoding="utf-8"
                ) as f:
                    # 添加manual_code到保存的信息中
                    manual_code = self.manual_code if self.manual_code else ""
                    user_info = f"{email},{password},{main_api_key},{manual_code}\n"
                    await f.write(user_info)
            self.logger.info(f"成功保存账号 {email} 到 {self.account_file}")
        except Exception as e:
            self.logger.error(f"Failed to save user {email}: {str(e)}")

    async def register_single_account(self, email: str, page: Page):
        """注册单个账户的完整流程"""
        try:
            self.logger.info(f"开始注册新账户: {email} - {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")

            # 设置邮箱地址
            self.email_address = email

            # 生成密码
            password = self.generate_password()
            self.logger.info(f"生成密码: {password}")

            # 1. 注册账户
            if not await self.register_account(page, password):
                return False

            # 2. 获取激活链接
            activation_link = await self.get_activation_link()
            if not activation_link:
                return False

            # 3. 激活邮箱
            if not await self.activate_email(page, activation_link):
                return False

            # 4. 设置2FA
            if not await self.setup_2fa(page):
                return False

            # 5. 获取API密钥
            api_keys = await self.get_api_keys(page)
            if not api_keys:
                self.logger.error("未获取到API密钥")
                return False

            # 6. 保存账号信息
            await self.save(email, password, api_keys)

            # 7. 显示成功信息
            self.logger.info("账户注册完成!")
            self.logger.info(f"邮箱: {self.email_address}")
            self.logger.info(f"密码: {password}")
            if self.manual_code:
                self.logger.info(f"2FA手动码: {self.manual_code}")
            self.logger.info(f"API密钥:")
            for key_info in api_keys:
                key = key_info.get('key', '')
                self.logger.info(f"   {key[:15]}...{key[-8:] if len(key) > 23 else key}")

            return True

        except Exception as e:
            self.logger.error(f"注册流程异常: {e}")
            await self.save_error_account(email)
            return False

    async def register_accounts(self, playwright, register_count: int = 5):
        """注册多个账号"""
        registered_users = []

        # 获取可用账号
        available_accounts = await self.get_available_accounts()
        if not available_accounts:
            self.logger.info("没有可用的账号，跳过注册")
            return registered_users

        # 开始注册账号
        for i in range(min(register_count, len(available_accounts))):
            account = available_accounts[i]
            email = account.get("email")

            self.logger.info(f"Registering account {i+1}/{register_count}: {email}")

            # 生成浏览器指纹
            fingerprint = self.fingerprint_generator.generate_fingerprint()
            browser_options = self.fingerprint_generator.get_browser_options(fingerprint)

            # 启动浏览器
            browser = await playwright.chromium.launch(**browser_options)
            context = await browser.new_context(
                viewport=fingerprint["viewport"],
                user_agent=fingerprint["user_agent"],
                locale=fingerprint["locale"],
                timezone_id=fingerprint["timezone_id"]
            )

            # 注入反检测脚本
            #await self.fingerprint_generator.inject_anti_detection_script(context, fingerprint)

            page = await context.new_page()

            try:
                # 注册账号
                success = await self.register_single_account(email, page)

                if success:
                    registered_users.append(email)
                    self.logger.info(f"Targon account {i+1} Created Successfully: {email}")

            except Exception as e:
                self.logger.error(f"注册账号 {email} 时出错: {e}")
                await self.save_error_account(email)
            finally:
                await browser.close()

            # 等待下一次注册
            if i < min(register_count, len(available_accounts)) - 1:
                wait_time = 10  # 等待10秒
                self.logger.info(f"Waiting {wait_time} seconds before registering the next account...")
                await asyncio.sleep(wait_time)

        return registered_users

    async def do(self, playwright, register_count: int = 5):
        """执行注册任务"""
        registered_users = []
        success_count = 0

        # 获取可用账号
        available_accounts = await self.get_available_accounts()
        if not available_accounts:
            self.logger.info("没有可用的账号，跳过注册")
            return registered_users

        self.logger.info(f"找到 {len(available_accounts)} 个可用账号")

        try:
            users = await self.register_accounts(playwright, register_count)
            registered_users.extend(users)
            success_count += len(users)
            self.logger.info(f"Registered {len(users)}/{register_count}")
        except Exception as e:
            self.logger.error(f"Registration task failed: {str(e)}")

        self.logger.info(f"Completed: {success_count}/{register_count}")
        return registered_users
 